"""
FastAPI router for call recording management endpoints.

Provides endpoints for:
- Getting and updating recording settings
- Downloading call recordings via signed URLs
- Listing calls with recording availability
"""
import logging
from uuid import UUID
from typing import List

from fastapi import APIRouter, Depends, HTTPException, Header, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, func

from db.session import get_db
from packages.shared.models import VoiceSettings, Call
from schemas.voice import (
    RecordingSettingsDTO,
    RecordingSettingsResponse,
    RecordingSettingsUpdate,
    SignedUrlDTO,
    CallRecordingDTO,
    CallListResponse
)
from services.recording_service import recording_service, RecordingServiceError

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/recording", tags=["recording"])


async def get_tenant_id(x_tenant_id: str = Header(..., alias="X-Tenant-ID")) -> UUID:
    """Extract tenant ID from header."""
    try:
        return UUID(x_tenant_id)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid tenant ID format")


@router.get(
    "/settings",
    response_model=RecordingSettingsResponse,
    summary="Get recording settings",
    description="Get the current call recording settings for the tenant"
)
async def get_recording_settings(
    tenant_id: UUID = Depends(get_tenant_id),
    db: AsyncSession = Depends(get_db)
):
    """Get recording settings for a tenant."""
    try:
        # Query voice settings for the tenant
        stmt = select(VoiceSettings).where(VoiceSettings.tenant_id == tenant_id)
        result = await db.execute(stmt)
        settings = result.scalar_one_or_none()
        
        # If no settings exist, create default settings
        if settings is None:
            settings = VoiceSettings(
                tenant_id=tenant_id,
                record_calls=True
            )
            db.add(settings)
            await db.commit()
            await db.refresh(settings)
            logger.info(f"Created default recording settings for tenant {tenant_id}")
        
        return RecordingSettingsResponse.from_orm(settings)
        
    except Exception as e:
        logger.error(f"Error getting recording settings for tenant {tenant_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.patch(
    "/settings",
    response_model=RecordingSettingsResponse,
    summary="Update recording settings",
    description="Update the call recording settings for the tenant"
)
async def update_recording_settings(
    settings_update: RecordingSettingsUpdate,
    tenant_id: UUID = Depends(get_tenant_id),
    db: AsyncSession = Depends(get_db)
):
    """Update recording settings for a tenant."""
    try:
        # Query existing settings
        stmt = select(VoiceSettings).where(VoiceSettings.tenant_id == tenant_id)
        result = await db.execute(stmt)
        settings = result.scalar_one_or_none()
        
        if settings is None:
            # Create new settings
            settings = VoiceSettings(
                tenant_id=tenant_id,
                record_calls=settings_update.record_calls
            )
            db.add(settings)
        else:
            # Update existing settings
            settings.record_calls = settings_update.record_calls
        
        await db.commit()
        await db.refresh(settings)
        
        logger.info(
            f"Updated recording settings for tenant {tenant_id}: "
            f"record_calls={settings_update.record_calls}"
        )
        
        return RecordingSettingsResponse.from_orm(settings)
        
    except Exception as e:
        logger.error(f"Error updating recording settings for tenant {tenant_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get(
    "/calls/{call_id}/download",
    response_model=SignedUrlDTO,
    summary="Get recording download URL",
    description="Get a signed URL for downloading a call recording"
)
async def get_recording_download_url(
    call_id: UUID,
    tenant_id: UUID = Depends(get_tenant_id),
    db: AsyncSession = Depends(get_db),
    expires_in: int = Query(600, description="URL expiration time in seconds", ge=60, le=3600)
):
    """Get a signed URL for downloading a call recording."""
    try:
        # Verify the call belongs to the tenant and has a recording
        stmt = select(Call).where(
            and_(
                Call.id == call_id,
                Call.firm_id == tenant_id,  # Assuming firm_id is the tenant identifier
                Call.recording_url.isnot(None)
            )
        )
        result = await db.execute(stmt)
        call = result.scalar_one_or_none()
        
        if call is None:
            raise HTTPException(
                status_code=404,
                detail="Call not found or no recording available"
            )
        
        # Check if recording has expired
        if call.expires_at and call.expires_at <= func.now():
            raise HTTPException(
                status_code=410,
                detail="Recording has expired and is no longer available"
            )
        
        # Generate signed URL
        try:
            signed_url = await recording_service.get_signed_url(
                call.recording_url,
                expires_in=expires_in
            )
            
            return SignedUrlDTO(url=signed_url, expires_in=expires_in)
            
        except RecordingServiceError as e:
            logger.error(f"Error generating signed URL for call {call_id}: {e}")
            raise HTTPException(status_code=500, detail="Failed to generate download URL")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting download URL for call {call_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get(
    "/calls",
    response_model=CallListResponse,
    summary="List calls with recording availability",
    description="List calls for the tenant with recording availability information"
)
async def list_calls_with_recordings(
    tenant_id: UUID = Depends(get_tenant_id),
    db: AsyncSession = Depends(get_db),
    limit: int = Query(50, description="Maximum number of calls to return", ge=1, le=100),
    offset: int = Query(0, description="Number of calls to skip", ge=0)
):
    """List calls for a tenant with recording availability."""
    try:
        # Query calls for the tenant
        stmt = (
            select(Call)
            .where(Call.firm_id == tenant_id)
            .order_by(Call.started_at.desc())
            .limit(limit)
            .offset(offset)
        )
        result = await db.execute(stmt)
        calls = result.scalars().all()
        
        # Get total count
        count_stmt = select(func.count(Call.id)).where(Call.firm_id == tenant_id)
        count_result = await db.execute(count_stmt)
        total = count_result.scalar()
        
        # Convert to DTOs
        call_dtos = []
        for call in calls:
            call_dto = CallRecordingDTO(
                id=call.id,
                recording_available=call.recording_url is not None,
                recording_url=call.recording_url,
                expires_at=call.expires_at,
                started_at=call.started_at,
                ended_at=call.ended_at,
                from_number=call.from_number,
                to_number=call.to_number
            )
            call_dtos.append(call_dto)
        
        return CallListResponse(calls=call_dtos, total=total)
        
    except Exception as e:
        logger.error(f"Error listing calls for tenant {tenant_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

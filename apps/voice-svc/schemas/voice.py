"""
Pydantic schemas for voice recording functionality.

These schemas handle validation and serialization for:
- Recording settings management
- Signed URL generation for downloads
- Call recording metadata
"""
from datetime import datetime
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, Field


class RecordingSettingsDTO(BaseModel):
    """Schema for recording settings."""
    
    record_calls: bool = Field(
        ...,
        description="Whether to record calls for this tenant"
    )


class RecordingSettingsResponse(BaseModel):
    """Schema for recording settings response."""
    
    tenant_id: UUID
    record_calls: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class RecordingSettingsUpdate(BaseModel):
    """Schema for updating recording settings."""
    
    record_calls: bool = Field(
        ...,
        description="Whether to record calls for this tenant"
    )


class SignedUrlDTO(BaseModel):
    """Schema for signed URL response."""
    
    url: str = Field(
        ...,
        description="Pre-signed URL for downloading the recording"
    )
    expires_in: int = Field(
        ...,
        description="Number of seconds until the URL expires",
        example=600
    )


class CallRecordingDTO(BaseModel):
    """Schema for call recording metadata."""
    
    id: UUID
    recording_available: bool = Field(
        ...,
        description="Whether a recording is available for this call"
    )
    recording_url: Optional[str] = Field(
        None,
        description="S3 URL of the recording (internal use only)"
    )
    expires_at: Optional[datetime] = Field(
        None,
        description="When the recording will be automatically deleted"
    )
    started_at: datetime
    ended_at: Optional[datetime] = None
    from_number: Optional[str] = None
    to_number: Optional[str] = None

    class Config:
        from_attributes = True


class CallListResponse(BaseModel):
    """Schema for call list with recording availability."""
    
    calls: list[CallRecordingDTO]
    total: int = Field(
        ...,
        description="Total number of calls for the tenant"
    )


class RecordingPurgeResult(BaseModel):
    """Schema for recording purge operation results."""
    
    purged_count: int = Field(
        ...,
        description="Number of recordings that were purged"
    )
    failed_count: int = Field(
        ...,
        description="Number of recordings that failed to purge"
    )
    total_processed: int = Field(
        ...,
        description="Total number of recordings processed"
    )

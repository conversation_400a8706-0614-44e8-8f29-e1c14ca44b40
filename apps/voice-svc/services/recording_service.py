"""
Recording service for call recording functionality.

This service handles:
- Checking if recording is enabled for a tenant
- Saving recordings to S3-compatible storage
- Generating signed URLs for downloads
- Purging expired recordings
"""
import logging
import os
from datetime import datetime, timedelta, timezone
from typing import Optional, BinaryIO
from uuid import UUID
import boto3
from botocore.exceptions import ClientError, NoCredentialsError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, and_
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

from packages.shared.models import VoiceSettings, Call
from schemas.voice import RecordingPurgeResult

logger = logging.getLogger(__name__)


class RecordingServiceError(Exception):
    """Base exception for recording service errors."""
    pass


class S3ConfigurationError(RecordingServiceError):
    """Raised when S3 configuration is invalid."""
    pass


class RecordingService:
    """Service for managing call recordings."""
    
    def __init__(self):
        """Initialize the recording service with S3 configuration."""
        self.aws_access_key_id = os.getenv("AWS_ACCESS_KEY_ID")
        self.aws_secret_access_key = os.getenv("AWS_SECRET_ACCESS_KEY")
        self.s3_bucket = os.getenv("S3_BUCKET_RECORDINGS")
        self.aws_region = os.getenv("AWS_REGION", "us-east-1")
        
        if not all([self.aws_access_key_id, self.aws_secret_access_key, self.s3_bucket]):
            raise S3ConfigurationError(
                "Missing required S3 configuration: AWS_ACCESS_KEY_ID, "
                "AWS_SECRET_ACCESS_KEY, and S3_BUCKET_RECORDINGS must be set"
            )
        
        self.s3_client = boto3.client(
            's3',
            aws_access_key_id=self.aws_access_key_id,
            aws_secret_access_key=self.aws_secret_access_key,
            region_name=self.aws_region
        )
        
        # Recording retention period (30 days)
        self.retention_days = 30
    
    async def should_record(self, tenant_id: UUID, db: AsyncSession) -> bool:
        """
        Check if recording is enabled for a tenant.
        
        Args:
            tenant_id: UUID of the tenant
            db: Database session
            
        Returns:
            True if recording is enabled, False otherwise
        """
        try:
            # Query voice settings for the tenant
            stmt = select(VoiceSettings).where(VoiceSettings.tenant_id == tenant_id)
            result = await db.execute(stmt)
            settings = result.scalar_one_or_none()
            
            # If no settings exist, create default settings (record_calls=True)
            if settings is None:
                settings = VoiceSettings(
                    tenant_id=tenant_id,
                    record_calls=True
                )
                db.add(settings)
                await db.commit()
                await db.refresh(settings)
                logger.info(f"Created default voice settings for tenant {tenant_id}")
                return True
            
            return settings.record_calls
            
        except Exception as e:
            logger.error(f"Error checking recording settings for tenant {tenant_id}: {e}")
            # Default to not recording on error
            return False
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=1, max=10),
        retry=retry_if_exception_type((ClientError,)),
        reraise=True
    )
    async def save_recording(
        self, 
        tenant_id: UUID, 
        call_id: UUID, 
        audio_data: BinaryIO,
        db: AsyncSession
    ) -> str:
        """
        Save a recording to S3 and update the call record.
        
        Args:
            tenant_id: UUID of the tenant
            call_id: UUID of the call
            audio_data: Binary audio data to save
            db: Database session
            
        Returns:
            S3 URL of the saved recording
            
        Raises:
            RecordingServiceError: If saving fails
        """
        try:
            # Generate S3 key
            s3_key = f"recordings/{tenant_id}/{call_id}.wav"
            
            # Upload to S3
            self.s3_client.upload_fileobj(
                audio_data,
                self.s3_bucket,
                s3_key,
                ExtraArgs={
                    'ContentType': 'audio/wav',
                    'ServerSideEncryption': 'AES256'
                }
            )
            
            # Generate S3 URL
            s3_url = f"s3://{self.s3_bucket}/{s3_key}"
            
            # Calculate expiration date (30 days from now)
            expires_at = datetime.now(timezone.utc) + timedelta(days=self.retention_days)
            
            # Update call record with recording URL and expiration
            stmt = (
                update(Call)
                .where(Call.id == call_id)
                .values(
                    recording_url=s3_url,
                    expires_at=expires_at
                )
            )
            await db.execute(stmt)
            await db.commit()
            
            logger.info(f"Saved recording for call {call_id} to {s3_url}")
            return s3_url
            
        except ClientError as e:
            error_msg = f"Failed to upload recording to S3: {e}"
            logger.error(error_msg)
            raise RecordingServiceError(error_msg)
        except Exception as e:
            error_msg = f"Unexpected error saving recording: {e}"
            logger.error(error_msg)
            raise RecordingServiceError(error_msg)
    
    @retry(
        stop=stop_after_attempt(2),
        wait=wait_exponential(multiplier=1, min=1, max=5),
        retry=retry_if_exception_type((ClientError,)),
        reraise=True
    )
    async def get_signed_url(self, s3_key: str, expires_in: int = 600) -> str:
        """
        Generate a signed URL for downloading a recording.
        
        Args:
            s3_key: S3 key of the recording
            expires_in: URL expiration time in seconds (default: 10 minutes)
            
        Returns:
            Pre-signed URL for downloading
            
        Raises:
            RecordingServiceError: If URL generation fails
        """
        try:
            # Remove s3:// prefix if present
            if s3_key.startswith("s3://"):
                s3_key = s3_key.replace(f"s3://{self.s3_bucket}/", "")
            
            url = self.s3_client.generate_presigned_url(
                'get_object',
                Params={'Bucket': self.s3_bucket, 'Key': s3_key},
                ExpiresIn=expires_in
            )
            
            logger.info(f"Generated signed URL for {s3_key}, expires in {expires_in}s")
            return url
            
        except ClientError as e:
            error_msg = f"Failed to generate signed URL: {e}"
            logger.error(error_msg)
            raise RecordingServiceError(error_msg)
    
    async def purge_expired_recordings(self, db: AsyncSession) -> RecordingPurgeResult:
        """
        Purge recordings that have expired (older than 30 days).
        
        Args:
            db: Database session
            
        Returns:
            RecordingPurgeResult with purge statistics
        """
        purged_count = 0
        failed_count = 0
        total_processed = 0
        
        try:
            # Find expired recordings
            cutoff_date = datetime.now(timezone.utc)
            stmt = select(Call).where(
                and_(
                    Call.recording_url.isnot(None),
                    Call.expires_at <= cutoff_date
                )
            )
            result = await db.execute(stmt)
            expired_calls = result.scalars().all()
            
            total_processed = len(expired_calls)
            logger.info(f"Found {total_processed} expired recordings to purge")
            
            for call in expired_calls:
                try:
                    # Extract S3 key from URL
                    s3_key = call.recording_url.replace(f"s3://{self.s3_bucket}/", "")
                    
                    # Delete from S3
                    self.s3_client.delete_object(Bucket=self.s3_bucket, Key=s3_key)
                    
                    # Update database record
                    stmt = (
                        update(Call)
                        .where(Call.id == call.id)
                        .values(recording_url=None, expires_at=None)
                    )
                    await db.execute(stmt)
                    
                    purged_count += 1
                    logger.info(f"Purged recording for call {call.id}")
                    
                except Exception as e:
                    failed_count += 1
                    logger.error(f"Failed to purge recording for call {call.id}: {e}")
            
            await db.commit()
            
            logger.info(
                f"Purge completed: {purged_count} purged, "
                f"{failed_count} failed, {total_processed} total"
            )
            
            return RecordingPurgeResult(
                purged_count=purged_count,
                failed_count=failed_count,
                total_processed=total_processed
            )
            
        except Exception as e:
            logger.error(f"Error during recording purge: {e}")
            await db.rollback()
            raise RecordingServiceError(f"Purge operation failed: {e}")


# Global instance
recording_service = RecordingService()

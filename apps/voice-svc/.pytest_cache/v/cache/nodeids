["tests/external_numbers/test_call_verification.py::TestCallVerificationService::TestGetVerificationStatus::test_get_verification_status_found", "tests/external_numbers/test_call_verification.py::TestCallVerificationService::TestGetVerificationStatus::test_get_verification_status_not_found", "tests/external_numbers/test_call_verification.py::TestCallVerificationService::TestGetVerificationStatus::test_get_verification_status_verified_number", "tests/external_numbers/test_call_verification.py::TestCallVerificationService::TestHandleIncomingCall::test_handle_incoming_call_failed_number", "tests/external_numbers/test_call_verification.py::TestCallVerificationService::TestHandleIncomingCall::test_handle_incoming_call_normal_number", "tests/external_numbers/test_call_verification.py::TestCallVerificationService::TestHandleIncomingCall::test_handle_incoming_call_pending_number", "tests/external_numbers/test_call_verification.py::TestCallVerificationService::TestHandleIncomingCall::test_handle_incoming_call_verified_number", "tests/external_numbers/test_call_verification.py::TestCallVerificationService::TestHandleIncomingCall::test_handle_incoming_call_verifying_number", "tests/external_numbers/test_call_verification.py::TestCallVerificationService::TestProcessVerificationInput::test_process_verification_input_invalid_code", "tests/external_numbers/test_call_verification.py::TestCallVerificationService::TestProcessVerificationInput::test_process_verification_input_number_not_found", "tests/external_numbers/test_call_verification.py::TestCallVerificationService::TestProcessVerificationInput::test_process_verification_input_success", "tests/external_numbers/test_call_verification.py::TestCallVerificationService::TestPromptGeneration::test_get_verification_failure_message", "tests/external_numbers/test_call_verification.py::TestCallVerificationService::TestPromptGeneration::test_get_verification_prompt", "tests/external_numbers/test_call_verification.py::TestCallVerificationService::TestPromptGeneration::test_get_verification_success_message", "tests/external_numbers/test_external_endpoints.py::TestCreateExternalNumber::test_create_external_number_duplicate", "tests/external_numbers/test_external_endpoints.py::TestCreateExternalNumber::test_create_external_number_invalid_did[+123]", "tests/external_numbers/test_external_endpoints.py::TestCreateExternalNumber::test_create_external_number_invalid_did[+1900555123]", "tests/external_numbers/test_external_endpoints.py::TestCreateExternalNumber::test_create_external_number_invalid_did[1234567890]", "tests/external_numbers/test_external_endpoints.py::TestCreateExternalNumber::test_create_external_number_invalid_did[]", "tests/external_numbers/test_external_endpoints.py::TestCreateExternalNumber::test_create_external_number_missing_tenant_id", "tests/external_numbers/test_external_endpoints.py::TestCreateExternalNumber::test_create_external_number_success", "tests/external_numbers/test_external_endpoints.py::TestGetSipUri::test_get_sip_uri_not_found", "tests/external_numbers/test_external_endpoints.py::TestGetSipUri::test_get_sip_uri_success", "tests/external_numbers/test_external_endpoints.py::TestListExternalNumbers::test_list_external_numbers_empty", "tests/external_numbers/test_external_endpoints.py::TestListExternalNumbers::test_list_external_numbers_success", "tests/external_numbers/test_external_endpoints.py::TestVerifyExternalNumber::test_verify_external_number_invalid_code", "tests/external_numbers/test_external_endpoints.py::TestVerifyExternalNumber::test_verify_external_number_not_found", "tests/external_numbers/test_external_endpoints.py::TestVerifyExternalNumber::test_verify_external_number_success", "tests/external_numbers/test_external_service.py::TestExternalNumberService::TestCreateExternalNumber::test_create_external_number_duplicate", "tests/external_numbers/test_external_service.py::TestExternalNumberService::TestCreateExternalNumber::test_create_external_number_invalid_did[+123-Phone number must be in E.164 format]", "tests/external_numbers/test_external_service.py::TestExternalNumberService::TestCreateExternalNumber::test_create_external_number_invalid_did[+1900555123-Premium numbers are not allowed]", "tests/external_numbers/test_external_service.py::TestExternalNumberService::TestCreateExternalNumber::test_create_external_number_invalid_did[+1976555123-Premium numbers are not allowed]", "tests/external_numbers/test_external_service.py::TestExternalNumberService::TestCreateExternalNumber::test_create_external_number_invalid_did[-Phone number is required]", "tests/external_numbers/test_external_service.py::TestExternalNumberService::TestCreateExternalNumber::test_create_external_number_invalid_did[1234567890-Phone number must be in E.164 format]", "tests/external_numbers/test_external_service.py::TestExternalNumberService::TestCreateExternalNumber::test_create_external_number_success", "tests/external_numbers/test_external_service.py::TestExternalNumberService::TestMarkNumberVerifying::test_mark_number_verifying_not_found", "tests/external_numbers/test_external_service.py::TestExternalNumberService::TestMarkNumberVerifying::test_mark_number_verifying_success", "tests/external_numbers/test_external_service.py::TestExternalNumberService::TestUtilityMethods::test_generate_sip_uri", "tests/external_numbers/test_external_service.py::TestExternalNumberService::TestUtilityMethods::test_generate_sip_uri_custom_domain", "tests/external_numbers/test_external_service.py::TestExternalNumberService::TestUtilityMethods::test_generate_verification_code", "tests/external_numbers/test_external_service.py::TestExternalNumberService::TestUtilityMethods::test_validate_e164_formatting[+****************-+15558675309]", "tests/external_numbers/test_external_service.py::TestExternalNumberService::TestUtilityMethods::test_validate_e164_formatting[+1 555 867 5309-+15558675309]", "tests/external_numbers/test_external_service.py::TestExternalNumberService::TestUtilityMethods::test_validate_e164_formatting[+1-555-867-5309-+15558675309]", "tests/external_numbers/test_external_service.py::TestExternalNumberService::TestUtilityMethods::test_validate_e164_formatting[+15558675309-+15558675309]", "tests/external_numbers/test_external_service.py::TestExternalNumberService::TestVerifyCode::test_verify_code_invalid_code", "tests/external_numbers/test_external_service.py::TestExternalNumberService::TestVerifyCode::test_verify_code_number_not_found", "tests/external_numbers/test_external_service.py::TestExternalNumberService::TestVerifyCode::test_verify_code_success", "tests/telnyx/test_forwarding_endpoints.py::TestForwardingEndpoints::test_configure_forwarding_inactive_number", "tests/telnyx/test_forwarding_endpoints.py::TestForwardingEndpoints::test_configure_forwarding_invalid_format", "tests/telnyx/test_forwarding_endpoints.py::TestForwardingEndpoints::test_configure_forwarding_number_not_found", "tests/telnyx/test_forwarding_endpoints.py::TestForwardingEndpoints::test_configure_forwarding_premium_rate", "tests/telnyx/test_forwarding_endpoints.py::TestForwardingEndpoints::test_configure_forwarding_self_forwarding", "tests/telnyx/test_forwarding_endpoints.py::TestForwardingEndpoints::test_configure_forwarding_success", "tests/telnyx/test_forwarding_endpoints.py::TestForwardingEndpoints::test_configure_forwarding_wrong_tenant", "tests/telnyx/test_forwarding_endpoints.py::TestForwardingEndpoints::test_configure_sip_inactive_number", "tests/telnyx/test_forwarding_endpoints.py::TestForwardingEndpoints::test_configure_sip_number_not_found", "tests/telnyx/test_forwarding_endpoints.py::TestForwardingEndpoints::test_configure_sip_success", "tests/telnyx/test_forwarding_endpoints.py::TestForwardingEndpoints::test_get_connection_status_not_found", "tests/telnyx/test_forwarding_endpoints.py::TestForwardingEndpoints::test_get_connection_status_success", "tests/telnyx/test_forwarding_endpoints.py::TestForwardingEndpoints::test_invalid_tenant_id_format", "tests/telnyx/test_forwarding_endpoints.py::TestForwardingEndpoints::test_missing_tenant_id_header", "tests/telnyx/test_forwarding_service.py::TestTelnyxForwardingService::test_attach_forwarding_api_error", "tests/telnyx/test_forwarding_service.py::TestTelnyxForwardingService::test_attach_forwarding_invalid_did", "tests/telnyx/test_forwarding_service.py::TestTelnyxForwardingService::test_attach_forwarding_invalid_forward_to", "tests/telnyx/test_forwarding_service.py::TestTelnyxForwardingService::test_attach_forwarding_number_not_found", "tests/telnyx/test_forwarding_service.py::TestTelnyxForwardingService::test_attach_forwarding_premium_rate_blocked", "tests/telnyx/test_forwarding_service.py::TestTelnyxForwardingService::test_attach_forwarding_self_forwarding", "tests/telnyx/test_forwarding_service.py::TestTelnyxForwardingService::test_attach_forwarding_success", "tests/telnyx/test_forwarding_service.py::TestTelnyxForwardingService::test_attach_sip_app_api_error", "tests/telnyx/test_forwarding_service.py::TestTelnyxForwardingService::test_attach_sip_app_invalid_did", "tests/telnyx/test_forwarding_service.py::TestTelnyxForwardingService::test_attach_sip_app_no_app_id", "tests/telnyx/test_forwarding_service.py::TestTelnyxForwardingService::test_attach_sip_app_success", "tests/test_recording_service.py::TestRecordingService::test_init_missing_config"]